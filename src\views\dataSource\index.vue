<template>
  <div class="app-container">
    <div class="datasource-top-row">
      <div class="datasource-header">数据源</div>
      <div style="display: flex; gap: 10px;">
        <el-button type="success" @click="testApiConnection">测试接口</el-button>
        <el-button type="primary" :icon="Plus" @click="createANewDataSource">新建数据源</el-button>
      </div>
    </div>
    <div class="datasource-main-content">
      <div class="left-content">
        <el-input v-model="search" style="width: 100%" :suffix-icon="Search" placeholder="搜索..." />
        <el-collapse v-model="activeNames" @change="collapseHandleChange" style="width: 100%; height: 100%">
          <!-- MySQL 数据源折叠项 -->
          <el-collapse-item name="1">
            <template #title>
              <div class="collapse-title">
                <span class="database-icon">🗄️</span>
                数据库
                <span class="universal-font-color">（{{ mysqlList.length }}）</span>
              </div>
            </template>
            <div v-for="item in mysqlList" :key="item.id"
              :class="item.id === collapseActive ? 'collapseItem  isActive' : ' collapseItem '"
              @click="selectDataItem(item)" @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false">
              <div class="details-title">{{ item.name }}</div>
              <div class="details">
                <span>{{ item.creator || '张大千' }}</span>
                <span>{{ formatDate(item.createTime) || '2025.02.03 18:20:23' }}</span>
              </div>
              <!-- 操作按钮 -->
              <div v-show="item.showActions" class="action-buttons" @click.stop>
                <el-icon class="action-icon edit-icon" @click="editDataSource(item)">
                  <Edit />
                </el-icon>
                <el-icon class="action-icon delete-icon" @click="confirmDeleteDataSource(item)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </el-collapse-item>
          <!-- 本地文件数据源折叠项 -->
          <el-collapse-item name="2">
            <template #title>
              <div class="collapse-title">
                <span class="database-icon">📄</span>
                本地文件
                <span class="universal-font-color">（{{ fileList.length }}）</span>
              </div>
            </template>
            <div v-for="item in fileList" :key="item.id"
              :class="item.id === collapseActive ? 'collapseItem  isActive' : 'collapseItem '"
              @click="selectDataItem(item)" @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false">
              <div class="details-title">{{ item.name }}</div>
              <div class="details">
                <span>{{ item.creator || '张大千' }}</span>
                <span>{{ formatDate(item.createTime) || '2025.02.03 18:20:23' }}</span>
              </div>
              <!-- 操作按钮 -->
              <div v-show="item.showActions" class="action-buttons" @click.stop>
                <el-icon class="action-icon delete-icon" @click="confirmDeleteDataSource(item)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </el-collapse-item>
          <!-- API 数据源折叠项 -->
          <el-collapse-item name="3">
            <template #title>
              <div class="collapse-title">
                <span class="database-icon">🔗</span>
                API
                <span class="universal-font-color">（{{ apiList.length }}）</span>
              </div>
            </template>
            <div v-for="item in apiList" :key="item.id"
              :class="item.id === collapseActive ? 'collapseItem  isActive' : 'collapseItem '"
              @click="selectDataItem(item)" @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false">
              <div class="details-title">{{ item.name }}</div>
              <div class="details">
                <span>{{ item.creator || '张大千' }}</span>
                <span>{{ formatDate(item.createTime) || '2025.02.03 18:20:23' }}</span>
              </div>
              <!-- 操作按钮 -->
              <div v-show="item.showActions" class="action-buttons" @click.stop>
                <el-icon class="action-icon edit-icon" @click="editDataSource(item)">
                  <Edit />
                </el-icon>
                <el-icon class="action-icon delete-icon" @click="confirmDeleteDataSource(item)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="right-content">
        <el-card v-if="!initialTip" class="newlyBuilt" shadow="never"
          body-style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
          <div class="empty-icon">📁</div>
          <p class="universal-font-color" style="margin: 16px 0 8px 0;">请选择左侧数据源列表查看详情</p>
          <el-alert type="warning" show-icon :closable="false"
            style="background: #f5f7fa; border-radius: 10px; margin-bottom: 0; width: auto;">
            <template #title>
              如没有所需要的数据源，可以点击
              <span class="creat-new-data" @click="createANewDataSource"
                style="color: #3b7bdf; cursor: pointer;">新建数据源</span>
            </template>
          </el-alert>
        </el-card>
        <el-card v-else style="width: 100%; box-shadow: none; border: none;">
          <el-row class="table-header" align="top" justify="space-between" style="margin-bottom: 10px;">
            <el-col :span="12">
              <div class="table-header-title">
                <p>{{ dataSourceDetail.name }}({{ dataSourceDetail.type }})</p>
                <p>
                  <span style="margin-right: 20px">所有者：{{ dataSourceDetail.creator }}</span>
                  <span>创建时间：{{ formatDate(dataSourceDetail.createTime) }}</span>
                </p>
              </div>
            </el-col>
            <el-col :span="6" style="display: flex; align-items: flex-start;">
              <el-input v-model="tableSearch" style="width: 90%;" :suffix-icon="Search" placeholder="搜索表名..." />
            </el-col>
            <el-col :span="6" style="display: flex; align-items: flex-start; justify-content: flex-end;">
              <el-button type="primary" @click="permissionClick">
                <el-icon style="margin-right: 6px"><Lock /></el-icon>
                权限分配
              </el-button>
            </el-col>
          </el-row>
          <GeneralTables :columns="mainColumns" :data="tableData" :border="true" :pagination="false"
            style="margin-top: 10px">
            <template #column-bb="scope">
              <el-tag :type="getTagType(scope.row.dataStatus)">{{ scope.row.dataStatus }}</el-tag>
            </template>
            <template #column-operation="scope">
              <el-button link type="primary" @click="previewTheData(scope.row)">预览数据</el-button>
              <el-button link type="primary" @click="createDataSet(scope.row)">创建数据集</el-button>
            </template>
          </GeneralTables>
          <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
            <el-pagination v-if="total > 0" :current-page="currentPage" :page-size="pageSize" :total="total"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
              @current-change="handleCurrentChange" @size-change="handleSizeChange" />
          </div>
        </el-card>
      </div>

      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="80%" :show-close="false">
        <template #header="{ titleId, titleClass }">
          <div class="my-header">
            <span :id="titleId" :class="titleClass">{{ dialogTitle }}</span>
            <el-icon style="width: 18px; height: 18px" @click="dialogVisible = false"><Close /></el-icon>
          </div>
        </template>

        <!-- Tab切换 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="数据预览" name="data">
            <GeneralTables :columns="dataPreviewColumns" :data="dataPreviewTableData" :border="true" :stripe="true"
              :pagination="false" style="margin-top: 10px" />
          </el-tab-pane>
          <el-tab-pane label="字段详情" name="field">
            <GeneralTables :columns="previewColumns" :data="previewTableData" :border="true" :stripe="true"
              :pagination="false" style="margin-top: 10px" />
          </el-tab-pane>

        </el-tabs>

        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 编辑数据源对话框 -->
      <el-dialog v-model="editDialogVisible" title="编辑数据源" width="400px" :show-close="false">
        <template #header="{ titleId, titleClass }">
          <div class="my-header">
            <span :id="titleId" :class="titleClass">编辑数据源</span>
            <el-icon style="width: 18px; height: 18px" @click="editDialogVisible = false">
              <Close />
            </el-icon>
          </div>
        </template>
        <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="80px">
          <el-form-item label="数据源名称" prop="name">
            <el-input v-model="editForm.name" placeholder="请输入数据源名称" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="editDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleUpdateDataSource">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from 'vue';
import { Search, Plus, Edit, Delete, Close, Lock } from '@element-plus/icons-vue';
import GeneralTables from '@/components/GeneralTables/index.vue';
import { getDataSourceList, getDataSourceWithTables, getFieldDetail, getDataDetail, updateDataSource, deleteDataSource, getDatabaseConfig } from '@/api/datainfor/dataupload';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { testApiConnection as testApi } from '@/utils/test-api.js';

let activeNames = ref(['1']);
let search = ref('');
let tableSearch = ref(''); // 右侧表格搜索
let mysqlList = ref([]);
let apiList = ref([]);
let fileList = ref([]);
let collapseActive = ref('');
let tableData = ref([]);
let dialogVisible = ref(false);
let dialogTitle = ref('');
let initialTip = ref(false);
let previewTableData = ref([]);
let dataSourceDetail = ref({
  dataTableList: [],
  creator: '',
  createTime: '',
  name: '',
  type: '',
  id: '',
});
let pageSize = ref(10);
let currentPage = ref(1);
let total = ref(0);
let currentDataSourceId = ref('');
let router = useRouter();

// 搜索防抖定时器
let searchTimer = null;
let tableSearchTimer = null;

// 编辑数据源相关变量
let editDialogVisible = ref(false);
let editFormRef = ref(null);
let editForm = reactive({
  id: '',
  name: ''
});
let editRules = reactive({
  name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }]
});

// 弹窗tab相关变量
let activeTab = ref('field');
let dataPreviewTableData = ref([]);
let dataPreviewColumns = ref([]);
let currentTableInfo = ref({
  databaseId: '',
  tableName: '',
  dataSourceId: null,
  id: ''
});

onMounted(() => {
  getDataList();
});

// 监听搜索框变化
watch(search, () => {
  searchDataSources();
});

watch(tableSearch, () => {
  searchTableData();
});

// 搜索数据源（调用API，带防抖）
const searchDataSources = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，500ms后执行搜索
  searchTimer = setTimeout(() => {
    const keyword = search.value.trim();
    getDataList(keyword);
  }, 500);
};

// 搜索表格数据（调用API，带防抖）
const searchTableData = () => {
  // 清除之前的定时器
  if (tableSearchTimer) {
    clearTimeout(tableSearchTimer);
  }

  // 设置新的定时器，500ms后执行搜索
  tableSearchTimer = setTimeout(() => {
    if (currentDataSourceId.value) {
      currentPage.value = 1; // 搜索时重置页码
      fetchTableData(currentDataSourceId.value, 1, pageSize.value);
    }
  }, 500);
};

const formatDate = timestamp => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};

const getDataList = async (searchKeyword = '') => {
  try {
    const params = searchKeyword ? { dataSourceName: searchKeyword } : {};
    const res = await getDataSourceList(params);
    console.log("获取数据源列表", res);
    if (res) {
      // 为每个数据源项添加showActions属性
      // 根据实际后端返回的数据结构调整
      apiList.value = (res.API || res.api || []).map(item => ({ ...item, showActions: false }));
      mysqlList.value = (res.DataBase || res.database || res.dataBase || []).map(item => ({ ...item, showActions: false }));
      fileList.value = (res.Excel || res.excel || res.file || []).map(item => ({ ...item, showActions: false }));
    }
  } catch (error) {
    console.error('获取数据源列表失败:', error);
    // 设置空数组避免页面报错
    apiList.value = [];
    mysqlList.value = [];
    fileList.value = [];
  }
}

const selectDataItem = item => {
  initialTip.value = true;
  currentDataSourceId.value = item.id;
  currentPage.value = 1;
  fetchTableData(item.id, 1, pageSize.value);
  collapseActive.value = item.id;
};

const createANewDataSource = () => {
  ElMessage.info('新建数据源功能待实现');
};

// 测试接口连通性
const testApiConnection = async () => {
  try {
    ElMessage.info('正在测试接口连通性...');
    const results = await testApi();

    if (results.dataSourceList.success && results.dataSetList.success) {
      ElMessage.success('所有接口连通正常！');
      console.log('接口测试成功，数据源响应:', results.dataSourceList.response);
      console.log('接口测试成功，数据集响应:', results.dataSetList.response);
    } else {
      let errorDetails = [];
      if (!results.dataSourceList.success) {
        errorDetails.push(`数据源接口失败: ${results.dataSourceList.error}`);
      }
      if (!results.dataSetList.success) {
        errorDetails.push(`数据集接口失败: ${results.dataSetList.error}`);
      }

      const errorMsg = errorDetails.join('\n');
      console.error('接口测试失败详情:', errorDetails);

      ElMessageBox.alert(errorMsg, '接口连通性测试结果', {
        confirmButtonText: '确定',
        type: 'warning'
      });
    }
  } catch (error) {
    console.error('测试接口连通性失败:', error);
    ElMessage.error('测试接口连通性失败: ' + (error.message || error.toString()));
  }
};

const collapseHandleChange = () => { };

const permissionClick = () => {
  ElMessage.info('权限分配功能待实现');
};

const previewTheData = async row => {
  console.log('预览数据参数:', row);

  try {
    // 保存当前表信息
    currentTableInfo.value = {
      databaseId: row.databaseId,
      tableName: row.tableProp,
      dataSourceId: row.dataSourceId,
      id: row.id
    };

    // 根据数据源类型构建不同的参数
    let params;
    if (row.id) {
      // 本地文件类型数据源
      params = {
        sheetId: row.id,
        dataBaseId: -1,
        tableName: row.tableProp
      };
    } else {
      // 数据库类型数据源
      params = {
        dataBaseId: row.databaseId,
        tableName: row.tableProp
      };
    }

    // 获取字段详情
    const res = await getFieldDetail(params);

    if (res) {
      previewTableData.value = res;
      dialogVisible.value = true;
      dialogTitle.value = row.tableLabel;
      activeTab.value = 'field'; // 默认显示字段详情

      // 清空数据预览相关数据
      dataPreviewTableData.value = [];
      dataPreviewColumns.value = [];
    }
  } catch (error) {
    console.error('获取字段详情失败:', error);
    ElMessage.error('获取字段详情失败');
  }
};

const createDataSet = row => {
  console.log('创建数据集参数:', row);
  router.push({
    path: '/dataset',
    query: {
      dataSourceId: row.dataSourceId,
      tableLabel: row.tableLabel,
      tableProp: row.tableProp
    }
  });
};

const getTagType = status => {
  switch (status) {
    case "正常":
      return 'primary';
    case "访问异常":
      return 'warning';
    case "字段变更":
      return 'danger';
    default:
      return 'info'; // 默认类型
  }
};

// 主表格列配置
const mainColumns = [
  {
    prop: 'tableProp',
    label: '数据表名称',
    width: '200',
  },
  {
    prop: 'tableLabel',
    label: '数据表标签',
    width: '200',
  },
  {
    prop: 'creator',
    label: '创建人',
  },
  {
    prop: 'updateTime',
    label: '更新时间',
  },
  {
    prop: 'dataStatus',
    label: '数据状态',
    slot: true,
  },
  {
    prop: 'operation',
    label: '操作',
    slot: true,
    width: '200',
  },
];

// 预览数据表格列配置
const previewColumns = [
  {
    prop: 'COLUMN_NAME',
    label: '字段名称',
  },
  {
    prop: 'DATA_TYPE',
    label: '字段类型',
  },
  {
    prop: 'COLUMN_COMMENT',
    label: '字段描述',
  },
  {
    prop: 'DATA_LENGTH',
    label: '字段长度',
  },
];

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchTableData(currentDataSourceId.value, page, pageSize.value);
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchTableData(currentDataSourceId.value, 1, size);
};

const fetchTableData = async (id, pageNo, pageSizeParam) => {
  try {
    const params = {
      pageNo,
      pageSize: pageSizeParam
    };

    // 如果有表格搜索关键词，添加到参数中
    const keyword = tableSearch.value.trim();
    if (keyword) {
      params.tableName = keyword;
    }

    const res = await getDataSourceWithTables(id, params);
    if (res) {
      dataSourceDetail.value = res;
      const pageInfo = res.dataTablePageList || res.tableList || res;
      const list = pageInfo.list || pageInfo.data || pageInfo || [];

      const processedData = list.map(table => ({
        ...table, // 保留所有原始字段
        creator: table.creator || res.creator || '未知',
        updateTime: formatDate(table.updateTime || table.modifyTime),
        databaseId: table.databaseId || table.dataSourceId || id,
        dataSourceId: table.dataSourceId || id
      }));

      // 直接显示API返回的数据
      tableData.value = processedData;

      pageSize.value = pageInfo.pageSize || pageSizeParam || 10;
      total.value = pageInfo.total || pageInfo.totalCount || list.length || 0;
    }
  } catch (error) {
    console.error('获取数据源详情失败:', error);
    // 设置空数据避免页面报错
    tableData.value = [];
    total.value = 0;
  }
};

// 编辑数据源
const editDataSource = async (item) => {
  editForm.id = item.id;
  editForm.name = item.name;
  editDialogVisible.value = true;
};

// 确认删除数据源
const confirmDeleteDataSource = (item) => {
  ElMessageBox.confirm(
    '删除后15天可在回收站中恢复',
    `确认删除数据源"${item.name}"吗？`,
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleDeleteDataSource(item.id, item.name);
  }).catch(() => {
    // 用户取消删除
  });
};

// 处理更新数据源
const handleUpdateDataSource = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate((valid) => {
    if (valid) {
      updateDataSource({
        id: editForm.id,
        name: editForm.name
      }).then(res => {
        if (res) {
          ElMessage.success('数据源更新成功');
          editDialogVisible.value = false;
          getDataList(); // 刷新列表
        } else {
          ElMessage.error('数据源更新失败');
        }
      }).catch(error => {
        ElMessage.error('更新过程中发生错误：' + error.message);
      });
    }
  });
};

// 处理删除数据源
const handleDeleteDataSource = async (id, dataSourceName) => {
  try {
    const res = await deleteDataSource(id);

    // 检查返回结果
    if (res && Array.isArray(res) && res.length > 0) {
      // 有关联的数据集，不能删除
      ElMessage.warning('该数据源存在关联的数据集，无法删除');
    } else if (res !== null && res !== undefined) {
      // 删除成功（返回空数组或其他成功标识）
      console.log('删除成功', res);
      ElMessage.success('数据源删除成功');
      getDataList(); // 刷新列表
      // 如果删除的是当前选中的数据源，清空右侧内容
      if (currentDataSourceId.value === id) {
        initialTip.value = false;
        collapseActive.value = '';
      }
    } else {
      ElMessage.error('数据源删除失败');
    }
  } catch (error) {
    console.error('删除过程中发生错误：', error);
    ElMessage.error('删除过程中发生错误：' + error.message);
  }
};

// 处理tab切换
const handleTabClick = (tab) => {
  if (tab.name === 'data') {
    fetchDataPreview();
  }
};

// 获取数据预览
const fetchDataPreview = async () => {
  try {
    // 根据数据源类型构建不同的参数
    let params;
    if (currentTableInfo.value.id) {
      // 本地文件类型数据源
      params = {
        sheetId: currentTableInfo.value.id,
        dataBaseId: -1,
        pageNo: 1,
        pageSize: 10,
        tableName: currentTableInfo.value.tableName
      };
    } else {
      // 数据库类型数据源
      params = {
        dataBaseId: currentTableInfo.value.databaseId,
        pageNo: 1,
        pageSize: 10,
        tableName: currentTableInfo.value.tableName
      };
    }

    const res = await getDataDetail(params);

    if (res && res.list) {
      dataPreviewTableData.value = res.list;

      // 动态生成列配置
      if (res.list.length > 0) {
        const firstRow = res.list[0];
        dataPreviewColumns.value = Object.keys(firstRow).map(key => ({
          prop: key,
          label: key,
          width: '150'
        }));
      }
    }
  } catch (error) {
    console.error('获取数据预览失败:', error);
    ElMessage.error('获取数据预览失败');
  }
};
</script>

<style scoped lang="scss">
.app-container {
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgb(243, 245, 248);
  box-shadow: none;
  padding: 0;
}

.datasource-top-row {
  width: 100%;
  min-width: 1200px;
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 0 32px;
  box-sizing: border-box;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.datasource-header {
  font-size: 22px;
  font-weight: bold;
  text-align: left;
}

.datasource-main-content {
  flex: 1 1 0;
  display: flex;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.left-content {
  display: flex;
  flex-direction: column;
  width: 360px;
  padding: 20px;
  margin-right: 10px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 1px 1px 2px 0px rgba(82, 90, 102, 0.04), 2px 2px 8px 0px rgba(82, 90, 102, 0.08);
  height: 100%;
  overflow-y: auto;
}

.right-content {
  width: calc(100% - 370px);
  display: flex;
  justify-content: center;
  position: relative;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 1px 1px 2px 0px rgba(82, 90, 102, 0.04), 2px 2px 8px 0px rgba(82, 90, 102, 0.08);
  height: 100%;
  overflow-y: auto;
}

.universal-font-color {
  color: #999999;
}

.app-container {
  .my-header {
    display: flex;
    justify-content: space-between;
  }
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.database-icon {
  font-size: 16px;
}

.empty-icon {
  font-size: 80px;
  margin-bottom: 16px;
}

.left-content {
  .el-collapse {
    width: 90%;
    margin-top: 10px;
    flex: 1;
    overflow-y: auto;
    border: none;
    scrollbar-gutter: stable;

    :deep(.el-collapse-item) {
      border-radius: 10px !important;
      border: 1px solid #eee !important;
      margin-bottom: 10px;
      padding: 10px;
      background-color: #f5f7fa;
    }

    :deep(.el-collapse-item__header) {
      background-color: #f5f7fa;
      border: none;
    }

    :deep(.el-collapse-item__content) {
      background-color: #f5f7fa;
      padding-bottom: 0;
    }

    :deep(.el-collapse-item__wrap) {
      border: none;
      background-color: #f5f7fa;

      .collapseItem {
        width: 100%;
        margin: auto;
        padding: 15px;
        margin-top: 10px;
        border-radius: 10px;
        background: #fff;
        color: #666;
        border: 1px solid #fff;
        position: relative;
        cursor: pointer;

        .details-title {
          font-weight: 700;
          font-size: 14px;
        }

        .details {
          display: flex;
          justify-content: space-between;
          flex-wrap: nowrap;
          font-size: 12px;
        }

        .action-buttons {
          position: absolute;
          top: 8px;
          right: 8px;
          display: flex;
          gap: 8px;
          z-index: 10;

          .action-icon {
            width: 20px;
            height: 20px;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            &.edit-icon {
              color: #409eff;
              background-color: rgba(64, 158, 255, 0.1);

              &:hover {
                background-color: rgba(64, 158, 255, 0.2);
                transform: scale(1.1);
              }
            }

            &.delete-icon {
              color: #f56c6c;
              background-color: rgba(245, 108, 108, 0.1);

              &:hover {
                background-color: rgba(245, 108, 108, 0.2);
                transform: scale(1.1);
              }
            }
          }
        }
      }

      .isActive {
        border-color: #c6d0f7;
        background: #e7ecf9;
        color: #2744d6;
      }
    }
  }
}

.right-content {
  .newlyBuilt {
    width: 60%;
    height: 320px;
    border: 1px dashed #ccc;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .tipText {
      padding: 16px;
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      border-radius: 10px;
      font-size: 16px;

      .creat-new-data {
        font-size: 16px;
        color: #3b7bdf;
        padding: 0;
        border: none;
        cursor: pointer;
      }
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 10px;

    .table-header-title {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      flex: 1;
      margin-right: 20px;
      border: 10px;
      display: flex;
      flex-direction: column;
      padding: 20px;
      color: #fff;
      border-radius: 10px;

      p {
        margin: 0;
      }

      p:nth-child(1) {
        font-weight: 700;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
