<template>
  <div class="dataset-container">
    <!-- 顶部行: 标题, 搜索, 筛选切换, 创建时间, 新建数据集 -->
    <div class="dataset-top-row">
      <div class="dataset-header">数据集</div>
      <div class="dataset-actions-right">
        <!-- 搜索区 -->
        <el-input
          v-model="datasetName"
          :prefix-icon="Search"
          placeholder="请输入数据集名称"
          style="width: 200px; margin-right: 8px"
          @keyup.enter="handleSearch"
        />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button class="toolbar-btn-filter" @click="toggleAdvancedFilters">
          <el-icon><ArrowDownBold /></el-icon>
          筛选
        </el-button>
        <el-select
          v-model="createTimeFilter"
          placeholder="排序"
          class="toolbar-select-time"
          @change="handleSortFilterChange"
        >
          <el-option label="创建时间" value="createTime" />
          <el-option label="修改时间" value="updateTime" />
        </el-select>
        <el-button type="primary" class="toolbar-btn-new" @click="addDataset"
          >新建数据集</el-button
        >
      </div>
    </div>

    <!-- 高级筛选行: 创建者, 权限, 来源 (默认隐藏) -->
    <div class="dataset-advanced-filters" v-show="showAdvancedFilters">
      <div class="filter-item">
        <span class="filter-label">创建者</span>
        <el-select v-model="owner" placeholder="创建者" class="toolbar-select">
          <el-option label="所有者" value="all" />
          <el-option label="我" value="me" />
        </el-select>
      </div>
      <div class="filter-item">
        <span class="filter-label">权限</span>
        <div class="permission-buttons">
          <el-button
            :type="permission === 'all' ? 'primary' : ''"
            :plain="permission !== 'all'"
            size="small"
            @click="handlePermissionFilter('all')"
          >
            全部
          </el-button>
          <el-button
            :type="permission === 'view' ? 'primary' : ''"
            :plain="permission !== 'view'"
            size="small"
            @click="handlePermissionFilter('view')"
          >
            可查看
          </el-button>
          <el-button
            :type="permission === 'edit' ? 'primary' : ''"
            :plain="permission !== 'edit'"
            size="small"
            @click="handlePermissionFilter('edit')"
          >
            可编辑
          </el-button>
        </div>
      </div>
      <div class="filter-item">
        <span class="filter-label">来源</span>
        <el-select v-model="source" placeholder="来源" class="toolbar-select">
          <el-option label="全部来源" value="all" />
          <!-- 这里可以添加实际的来源选项 -->
        </el-select>
      </div>
    </div>

    <!-- 表格区 -->
    <el-table
      :data="tableData"
      stripe
      border
      class="dataset-table"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="50" />
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column prop="description" label="数据集描述" min-width="180" />
      <el-table-column prop="datasourceName" label="数据源" min-width="120" />
      <el-table-column prop="creator" label="创建者" min-width="100" />
      <el-table-column prop="updater" label="修改人" min-width="100" />
      <el-table-column prop="updateTime" label="修改时间" min-width="160">
        <template #default="scope">
          {{ formatDate(scope.row.updateTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="160">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200">
        <template #default="scope">
          <el-link type="primary" @click="addData(scope.row)">权限分配</el-link>
          <el-link type="primary" @click="editDataset(scope.row)">编辑</el-link>
          <el-link type="primary" @click="openAskNumberConfig(scope.row)"
            >问数配置</el-link
          >
          <el-link type="danger" @click="deleteDataset(scope.row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        v-if="total > 0"
        style="margin-top: 16px; text-align: right"
        background
        :current-page="pageNo"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import { getDataSetList, getDeleteDataSet } from "@/api/datainfor/dataset";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, ArrowDownBold } from "@element-plus/icons-vue";

const username = "zls";
const router = useRouter();

const owner = ref("all");
const permission = ref("all");
const searchText = ref("");
const createTimeFilter = ref("createTime"); // 排序筛选：创建时间/修改时间
const source = ref("all"); // 新增：来源筛选
const showAdvancedFilters = ref(false); // 新增：控制高级筛选区域的显示/隐藏

const tableData = ref([]);
const pageNo = ref(1);
const pageSize = ref(10);
const total = ref(0);
const datasetName = ref("");
const sortField = ref("");

onMounted(async () => {
  try {
    await getDataSetListData();
    console.log(tableData.value, "tableData");
    console.log();
    
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
});

// 格式化时间戳
const formatDate = (timestamp) => {
  if (!timestamp) return "-";
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

const getDataSetListData = async () => {
  try {
    const params = {
      datasetName: datasetName.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      sortField: sortField.value,
    };
    const res = await getDataSetList(params);
    if (res) {
      // 根据实际后端返回的数据结构调整
      tableData.value = res.list || res.data || res || [];
      total.value =
        res.total || res.totalCount || (res.list ? res.list.length : 0) || 0;
    }
  } catch (error) {
    console.error("获取数据集列表失败:", error);
    // 设置空数据避免页面报错
    tableData.value = [];
    total.value = 0;
  }
};

// 删除数据集
const deleteDataset = (row) => {
  console.log(row, "row");
  // 二次校验
  ElMessageBox.confirm("确定删除该数据集吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    getDeleteDataSet({ id: row.id }).then((res) => {
      if (res) {
        ElMessage.success("删除成功");
        getDataSetListData();
      }
    });
  });
};

// 切换高级筛选区域的显示/隐藏
const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value;
  // 如果隐藏高级筛选，重置高级筛选条件
  if (!showAdvancedFilters.value) {
    owner.value = "all";
    permission.value = "all";
    source.value = "all";
  }
};

// 处理权限筛选
const handlePermissionFilter = (permissionType) => {
  permission.value = permissionType;
  // 根据权限类型对数据进行排序/筛选
  filterAndSortData();
};

// 根据筛选条件对数据进行筛选和排序
const filterAndSortData = () => {
  // 这里可以调用API重新获取数据，或者对现有数据进行筛选
  // 示例：根据权限筛选数据
  console.log("当前权限筛选:", permission.value);
  console.log("当前创建者筛选:", owner.value);
  console.log("当前来源筛选:", source.value);

  // 重新获取数据列表
  getDataSetListData();
};

// 处理排序筛选变化
const handleSortFilterChange = (sortType) => {
  console.log("排序类型变化:", sortType);
  // 设置排序字段，默认降序排列（最新的在前）
  sortField.value = sortType;
  pageNo.value = 1;
  getDataSetListData();
};

const addDataset = () => {
  ElMessage.info("新建数据集功能待实现");
};

const editDataset = (row) => {
  console.log(row);
  ElMessage.info("编辑数据集功能待实现");
};

// 权限分配
const addData = (row) => {
  console.log("打开权限分配:", row);
  ElMessage.info("权限分配功能待实现");
};

// 问数配置
const openAskNumberConfig = (row) => {
  console.log("打开问数配置:", row);
  ElMessage.info("问数配置功能待实现");
};

function handlePageChange(newPage) {
  pageNo.value = newPage;
  getDataSetListData();
}

function handleSizeChange(newSize) {
  pageSize.value = newSize;
  pageNo.value = 1;
  getDataSetListData();
}

function handleSearch() {
  pageNo.value = 1;
  getDataSetListData();
}

function handleSortChange() {
  // 表格列排序已禁用，排序通过下拉选择器控制
  console.log("表格列排序已禁用，请使用排序下拉选择器");
}
</script>

<style scoped>
.dataset-container {
  background: #fff;
  padding: 32px 24px;
  min-height: 100vh;
}

.dataset-header {
  font-size: 22px;
  font-weight: bold;
  text-align: left;
}

.dataset-top-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.dataset-actions-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-input-search {
  width: 240px;
}

.toolbar-btn-filter {
  min-width: 80px;
}

.toolbar-select-time {
  width: 150px;
}

.toolbar-btn-new {
  min-width: 100px;
}

.pagination {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

.dataset-advanced-filters {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 18px;
  border-bottom: 1px solid #eee;
  /* 添加分隔线 */
  padding-bottom: 18px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  min-width: 40px;
}

.permission-buttons {
  display: flex;
  gap: 8px;
}

.permission-buttons .el-button {
  border-radius: 4px;
  font-size: 12px;
  padding: 5px 12px;
  height: 28px;
}

.permission-buttons .el-button--small {
  min-width: 60px;
}

.toolbar-select {
  width: 150px;
}

.dataset-table {
  margin-top: 8px;
}

.dataset-table .el-table__header th {
  font-weight: bold;
  background: #fafbfc;
}

.dataset-table .el-link {
  margin-right: 10px;
  font-size: 13px;
}
</style>
