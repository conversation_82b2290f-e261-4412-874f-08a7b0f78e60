<template>
  <div class="custom-table-container">
    <!-- 表格 -->
    <el-table
      :data="tableData"
      :height="height"
      :border="border"
      :stripe="stripe"
      :highlight-current-row="highlightCurrentRow"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      @sort-change="handleSortChange"
      :row-class-name="rowClassName"
      :header-cell-style="headerCellStyle"
      :show-header="showHeader"
      :row-key="rowKey"
      :key="innerTableKey"
    >
      <!-- 多选列 -->
      <el-table-column v-if="selection" type="selection" width="55" align="center" />

      <!-- 序号列 -->
      <el-table-column v-if="showIndex" type="index" :label="indexLabel" width="55" align="center" />

      <!-- 动态列 -->
      <template v-for="column in columns" :key="`${column.prop}-${tableKey}`">
        <!-- 普通列 -->
        <el-table-column
          v-if="!column.slot && !column.children"
          :key="`normal-${column.prop}-${tableKey}`"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :fixed="column.fixed"
          :align="column.align"
          :show-overflow-tooltip="column.tooltip || tooltip"
        >
          <!-- 表头插槽 -->
          <template #header="scope" v-if="column.headerSlot">
            <slot :name="`header-${column.prop}`" :column="column" :index="scope.columnIndex">
              {{ column.label }}
            </slot>
          </template>

          <!-- 自定义插槽 -->
          <template #default="scope">
            <slot :name="`column-${column.prop}`" :row="scope.row" :index="scope.$index">
              {{ scope.row[column.prop] }}
            </slot>
          </template>
        </el-table-column>

        <!-- 带子列的列 -->
        <el-table-column
          v-else-if="column.children"
          :key="`parent-${column.prop}-${tableKey}`"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :align="column.align"
        >
          <!-- 父列表头插槽 -->
          <template #header="scope" v-if="column.headerSlot">
            <slot :name="`header-${column.prop}`" :column="column" :index="scope.columnIndex">
              {{ column.label }}
            </slot>
          </template>

          <template v-for="child in column.children" :key="`${child.prop}-${tableKey}`">
            <el-table-column
              :prop="child.prop"
              :label="child.label"
              :width="child.width"
              :min-width="child.minWidth"
              :sortable="child.sortable"
              :fixed="child.fixed"
              :align="child.align"
              :show-overflow-tooltip="child.tooltip || tooltip"
            >
              <!-- 子列表头插槽 -->
              <template #header="scope" v-if="child.headerSlot">
                <slot :name="`header-${child.prop}`" :column="child" :index="scope.columnIndex">
                  {{ child.label }}
                </slot>
              </template>

              <!-- 自定义插槽 -->
              <template #default="scope">
                <slot :name="`column-${child.prop}`" :row="scope.row" :index="scope.$index">
                  {{ scope.row[child.prop] }}
                </slot>
              </template>
            </el-table-column>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column v-else :key="`operation-${column.prop}-${tableKey}`" :label="column.label" :width="column.width" :min-width="column.minWidth" :fixed="column.fixed" :align="column.align">
          <!-- 操作列表头插槽 -->
          <template #header="scope" v-if="column.headerSlot">
            <slot :name="`header-${column.prop}`" :column="column" :index="scope.columnIndex">
              {{ column.label }}
            </slot>
          </template>

          <template #default="scope">
            <slot :name="`column-${column.prop}`" :row="scope.row" :index="scope.$index"></slot>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页 -->
    <div class="pagination" v-if="pagination">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch} from 'vue';
import {ElTable, ElTableColumn, ElPagination} from 'element-plus';

// Props 定义
const props = defineProps({
  // 表格列配置
  columns: {
    type: Array,
    required: true,
  },
  // 表格数据
  data: {
    type: Array,
    default: () => [],
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: 'auto',
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: true,
  },
  // 是否斑马纹
  stripe: {
    type: Boolean,
    default: false,
  },
  // 是否高亮当前行
  highlightCurrentRow: {
    type: Boolean,
    default: false,
  },
  // 是否显示多选列
  selection: {
    type: Boolean,
    default: false,
  },
  // 是否显示序号列
  showIndex: {
    type: Boolean,
    default: false,
  },
  // 序号列的标题
  indexLabel: {
    type: String,
    default: '序号',
  },
  // 是否显示分页
  pagination: {
    type: Boolean,
    default: true,
  },
  // 分页布局
  paginationLayout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper',
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 10,
  },
  // 分页大小选项
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  // 总条目数
  total: {
    type: Number,
    default: 0,
  },
  // 是否显示表头tooltip
  tooltip: {
    type: Boolean,
    default: true,
  },
  // 表头样式
 headerCellStyle: {
  type: Object,
  default: () => ({
    backgroundColor: '#f5f7fa',
    color: '#303133',
  }),
},
  // 是否显示表头
  showHeader: {
    type: Boolean,
    default: true,
  },
  // 行键
  rowKey: {
    type: [String, Function],
    default: 'id',
  },
  // 添加 tableKey prop
  tableKey: {
    type: Number,
    default: 0
  }
});

// Emits 定义
const emit = defineEmits([
  'selection-change',
  'row-click',
  'sort-change',
  'size-change',
  'current-change',
  'row-class-name'
]);

// 添加一个计数器来强制重新渲染
const innerTableKey = ref(props.tableKey);
watch(() => props.tableKey, (newKey) => {
  innerTableKey.value = newKey;
})

// 分页相关的变量
const currentPage = ref(1);

// 计算表格数据（分页后的数据）
const tableData = computed(() => {
  const start = (currentPage.value - 1) * props.pageSize;
  const end = start + props.pageSize;
  return props.data.slice(start, end);
});

// 监听总条目数变化，重置当前页
watch(
  () => props.total,
  newTotal => {
    if (newTotal < (currentPage.value - 1) * props.pageSize) {
      currentPage.value = 1;
    }
  }
);

// 处理多选事件
const handleSelectionChange = (selection) => {
  emit('selection-change', selection);
};

// 处理行点击事件
const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event);
};

// 处理排序事件
const handleSortChange = (sort) => {
  emit('sort-change', sort);
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  emit('size-change', size);
};

// 处理分页页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  emit('current-change', page);
};

// 行类名
const rowClassName = ({row, rowIndex}) => {
  emit('row-class-name', row, rowIndex);
  return '';
};

let currentDataSourceId = ref('');
</script>

<style scoped>
.custom-table-container {
  width: 100%;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
