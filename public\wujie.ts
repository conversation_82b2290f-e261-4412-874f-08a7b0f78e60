/**
 * 无界微前端配置
 */
import { useUserStore } from '@/store/modules/user'
import { getAccessToken } from '@/utils/auth'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

// 微应用配置接口
export interface MicroAppConfig {
  name: string
  url: string
  alive?: boolean
  fetch?: (url: string, options?: any) => Promise<Response>
  props?: Record<string, any>
  attrs?: Record<string, any>
  replace?: (code: string) => string
  beforeLoad?: (appWindow: Window) => void
  beforeMount?: (appWindow: Window) => void
  afterMount?: (appWindow: Window) => void
  beforeUnmount?: (appWindow: Window) => void
  afterUnmount?: (appWindow: Window) => void
}

// 权限数据接口
export interface PermissionData {
  token: string
  userInfo: any
  permissions: string[]
  roles: string[]
  menus: any[]
}

// 获取权限数据
export const getPermissionData = (): PermissionData => {
  const userStore = useUserStore()
  const userInfo = wsCache.get(CACHE_KEY.USER)

  const accessToken = getAccessToken();
  console.log('getAccessToken返回值:', accessToken);
  console.log('userStore.getToken:', userStore.getToken);
  const permissionData = {
    token: accessToken || userStore.getToken || '',
    userInfo: userStore.getUser,
    permissions: Array.from(userStore.getPermissions),
    roles: userStore.getRoles,
    menus: userInfo?.menus || []
  }
  console.log('主应用生成的permissionData:', permissionData);
  return permissionData
}

// 权限验证函数
export const createPermissionValidator = () => {
  const userStore = useUserStore()
  const all_permission = '*:*:*'

  return {
    // 检查权限
    hasPermission: (permission: string | string[]): boolean => {
      if (!permission) return true

      const permissions = Array.isArray(permission) ? permission : [permission]
      return (
        userStore.permissions.has(all_permission) ||
        permissions.some((perm) => userStore.permissions.has(perm))
      )
    },

    // 检查角色
    hasRole: (role: string | string[]): boolean => {
      if (!role) return true

      const roles = Array.isArray(role) ? role : [role]
      const userRoles = userStore.getRoles
      const super_admin = 'super_admin'

      return userRoles.some((userRole: string) => {
        return super_admin === userRole || roles.includes(userRole)
      })
    },

    // 检查是否为超级管理员
    isSuperAdmin: (): boolean => {
      return userStore.getRoles.includes('super_admin')
    }
  }
}

// 微应用列表配置
export const microApps: Record<string, MicroAppConfig> = {
  // 数据源微应用
  datasource: {
    name: 'datasource',
    url: 'http://localhost:3000/datasource',
    alive: true, // 保活模式
    props: {
      // 传递给子应用的数据
      userInfo: null,
      token: null
    },
    attrs: {
      // DOM 属性
      style: 'width: 100%; height: 100%;'
    },
    // 生命周期钩子
    beforeLoad: (_appWindow: Window) => {
      console.log('数据源应用开始加载')
    },
    beforeMount: (appWindow: Window) => {
      console.log('数据源应用开始挂载')
      // 注入权限数据和验证函数到微应用
      const permissionData = getPermissionData()
      const permissionValidator = createPermissionValidator()

      // 在微应用的window对象上挂载权限相关数据和方法
      ;(appWindow as any).__MAIN_APP_PERMISSION__ = {
        ...permissionData,
        ...permissionValidator,
        // 权限检查的便捷方法
        checkAuth: (permission?: string | string[], role?: string | string[]) => {
          if (permission && !permissionValidator.hasPermission(permission)) {
            return false
          }
          if (role && !permissionValidator.hasRole(role)) {
            return false
          }
          return true
        }
      }
    },
    afterMount: (_appWindow: Window) => {
      console.log('数据源应用挂载完成')
    },
    beforeUnmount: (_appWindow: Window) => {
      console.log('数据源应用开始卸载')
    },
    afterUnmount: (_appWindow: Window) => {
      console.log('数据源应用卸载完成')
    }
  },

  // 数据集微应用
  dataset: {
    name: 'dataset',
    url: 'http://localhost:3000/dataset',
    alive: true, // 保活模式
    props: {
      // 传递给子应用的数据
      userInfo: null,
      token: null
    },
    attrs: {
      // DOM 属性
      style: 'width: 100%; height: 100%;'
    },
    // 生命周期钩子
    beforeLoad: (_appWindow: Window) => {
      console.log('数据集应用开始加载')
    },
    beforeMount: (appWindow: Window) => {
      console.log('数据集应用开始挂载')
      // 注入权限数据和验证函数到微应用
      const permissionData = getPermissionData()
      const permissionValidator = createPermissionValidator()

      // 在微应用的window对象上挂载权限相关数据和方法
      ;(appWindow as any).__MAIN_APP_PERMISSION__ = {
        ...permissionData,
        ...permissionValidator,
        // 权限检查的便捷方法
        checkAuth: (permission?: string | string[], role?: string | string[]) => {
          if (permission && !permissionValidator.hasPermission(permission)) {
            return false
          }
          if (role && !permissionValidator.hasRole(role)) {
            return false
          }
          return true
        }
      }
    },
    afterMount: (_appWindow: Window) => {
      console.log('数据集应用挂载完成')
    },
    beforeUnmount: (_appWindow: Window) => {
      console.log('数据集应用开始卸载')
    },
    afterUnmount: (_appWindow: Window) => {
      console.log('数据集应用卸载完成')
    }
  },

  // 知识库微应用
  knowledgeBase: {
    name: 'knowledge-base',
    url: 'http://localhost:3000/know1',
    alive: true, // 保活模式
    props: {
      // 传递给子应用的数据
      userInfo: null,
      token: null
    },
    attrs: {
      // DOM 属性
      style: 'width: 100%; height: 100%;'
    },
    // 生命周期钩子
    beforeLoad: (_appWindow: Window) => {
      console.log('知识库应用开始加载')
    },
    beforeMount: (appWindow: Window) => {
      console.log('知识库应用开始挂载')
      // 注入权限数据和验证函数到微应用
      const permissionData = getPermissionData()
      const permissionValidator = createPermissionValidator()

      // 在微应用的window对象上挂载权限相关数据和方法
      ;(appWindow as any).__MAIN_APP_PERMISSION__ = {
        ...permissionData,
        ...permissionValidator,
        // 权限检查的便捷方法
        checkAuth: (permission?: string | string[], role?: string | string[]) => {
          if (permission && !permissionValidator.hasPermission(permission)) {
            return false
          }
          if (role && !permissionValidator.hasRole(role)) {
            return false
          }
          return true
        }
      }
    },
    afterMount: (_appWindow: Window) => {
      console.log('知识库应用挂载完成')
    },
    beforeUnmount: (_appWindow: Window) => {
      console.log('知识库应用开始卸载')
    },
    afterUnmount: (_appWindow: Window) => {
      console.log('知识库应用卸载完成')
    }
  },

  // 业务名词管理微应用
  businessTerms: {
    name: 'business-terms',
    url: 'http://localhost:3000/know2',
    alive: true, // 保活模式
    props: {
      // 传递给子应用的数据
      userInfo: null,
      token: null
    },
    attrs: {
      // DOM 属性
      style: 'width: 100%; height: 100%;'
    },
    // 生命周期钩子
    beforeLoad: (_appWindow: Window) => {
      console.log('业务名词管理应用开始加载')
    },
    beforeMount: (appWindow: Window) => {
      console.log('业务名词管理应用开始挂载')
      // 注入权限数据和验证函数到微应用
      const permissionData = getPermissionData()
      const permissionValidator = createPermissionValidator()

      // 在微应用的window对象上挂载权限相关数据和方法
      ;(appWindow as any).__MAIN_APP_PERMISSION__ = {
        ...permissionData,
        ...permissionValidator,
        // 权限检查的便捷方法
        checkAuth: (permission?: string | string[], role?: string | string[]) => {
          if (permission && !permissionValidator.hasPermission(permission)) {
            return false
          }
          if (role && !permissionValidator.hasRole(role)) {
            return false
          }
          return true
        }
      }
    },
    afterMount: (_appWindow: Window) => {
      console.log('业务名词管理应用挂载完成')
    },
    beforeUnmount: (_appWindow: Window) => {
      console.log('业务名词管理应用开始卸载')
    },
    afterUnmount: (_appWindow: Window) => {
      console.log('业务名词管理应用卸载完成')
    }
  }
}

// 全局配置
export const wujieConfig = {
  // 预加载应用
  preloadApps: ['datasource', 'dataset', 'knowledge-base', 'business-terms'],
  
  // 全局错误处理
  errorHandler: (error: Error, appName: string) => {
    console.error(`微应用 ${appName} 发生错误:`, error)
  },
  
  // 全局加载处理
  loadingHandler: (loading: boolean, appName: string) => {
    console.log(`微应用 ${appName} 加载状态:`, loading ? '加载中' : '加载完成')
  }
}

// 获取微应用配置
export const getMicroAppConfig = (appName: string): MicroAppConfig | undefined => {
  return microApps[appName]
}

// 更新微应用props
export const updateMicroAppProps = (appName: string, props: Record<string, any>) => {
  if (microApps[appName]) {
    microApps[appName].props = { ...microApps[appName].props, ...props }
  }
}
