<template>
  <div class="dataset-container">
    <!-- 微应用容器 -->
    <div class="micro-app-container">
      <!-- 无界微前端容器 -->
      <WujieVue v-if="!error" ref="wujieRef" :name="appConfig.name" :url="appConfig.url" :alive="appConfig.alive"
        :props="appConfig.props" :attrs="appConfig.attrs" :beforeLoad="appConfig.beforeLoad"
        :beforeMount="appConfig.beforeMount" :afterMount="appConfig.afterMount" :beforeUnmount="appConfig.beforeUnmount"
        :afterUnmount="appConfig.afterUnmount" @load="handleLoad" @error="handleError" class="wujie-container" />

      <div v-if="error" class="error-container">
        <el-result icon="error" title="数据集应用加载失败" :sub-title="errorMessage">
          <template #extra>
            <el-button type="primary" @click="retryLoad">重新加载</el-button>
            <el-button @click="openInNewTab">新窗口打开</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import WujieVue from 'wujie-vue3'
import { getMicroAppConfig, updateMicroAppProps, getPermissionData } from '@/config/wujie'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'Dataset'
})

// 状态管理
const userStore = useUserStore()
const wujieRef = ref()
const loading = ref(true)
const error = ref(false)
const errorMessage = ref('')

// 获取应用配置
const appConfig = getMicroAppConfig('dataset')!

// 生命周期处理
const handleLoad = () => {
  loading.value = false
  error.value = false
  console.log('数据集应用加载完成')


}

const handleError = (err: Error) => {
  loading.value = false
  error.value = true
  errorMessage.value = err.message || '应用加载失败，请检查网络连接或应用服务状态'
  console.error('数据集应用加载错误:', err)
  ElMessage.error('数据集应用加载失败')
}

// 重新加载
const retryLoad = () => {
  loading.value = true
  error.value = false
  if (wujieRef.value) {
    wujieRef.value.reload()
  }
}

// 新窗口打开
const openInNewTab = () => {
  window.open(appConfig.url, '_blank')
}

// 组件挂载时更新props
onMounted(() => {
  // 获取完整的权限数据
  const permissionData = getPermissionData()
  console.log("55555555555555555555555555555",userStore.getUser, userStore.getToken, userStore.getPermissions, userStore.getRoles);
      console.log('主应用传递的token:', permissionData.token);

  // 传递权限数据给子应用
  const props = {
    ...permissionData,
    theme: 'light',
    userInfo: permissionData.userInfo,
    token: permissionData.token
  }

  updateMicroAppProps('dataset', props)
})

// 监听用户权限变化，实时更新微应用的权限数据
watch(
  () => [userStore.getUser, userStore.getToken, userStore.getPermissions, userStore.getRoles],
  () => {
    const permissionData = getPermissionData()
    const props = {
      ...permissionData,
      theme: 'light',
      userInfo: permissionData.userInfo,
      token: permissionData.token
    }
    updateMicroAppProps('dataset', props)
  },
  { deep: true }
)

// 组件卸载时清理
onUnmounted(() => {
  console.log('数据集页面组件卸载')
})
</script>

<style lang="scss" scoped>
.dataset-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.micro-app-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.error-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}

.wujie-container {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}
</style>
