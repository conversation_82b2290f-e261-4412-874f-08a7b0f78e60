import service from '@/utils/request.js'

/**
 * 获取数据集列表
 * @param {Object} params 查询参数
 * @param {string} params.datasetName 数据集名称（可选）
 * @param {number} params.pageNo 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.sortField 排序字段
 */
export const getDataSetList = async (params = {}) => {
  try {
    const response = await service.get('/api/dataset/list', { params })
    return response
  } catch (error) {
    console.error('获取数据集列表失败:', error)
    throw error
  }
};

/**
 * 删除数据集
 * @param {Object} params 删除参数
 * @param {string} params.id 数据集ID
 */
export const getDeleteDataSet = async (params) => {
  try {
    const response = await del(`/api/dataset/${params.id}`)
    return response
  } catch (error) {
    console.error('删除数据集失败:', error)
    throw error
  }
};
