import { get, put, del } from '@/config/api.js'

/**
 * 获取数据源列表
 * @param {Object} params 查询参数
 * @param {string} params.dataSourceName 数据源名称（可选）
 */
export const getDataSourceList = async (params = {}) => {
  try {
    const response = await get('/datasource/list', params)
    return response
  } catch (error) {
    console.error('获取数据源列表失败:', error)
    throw error
  }
};

/**
 * 获取数据源详情及其数据表列表
 * @param {string} id 数据源ID
 * @param {Object} params 查询参数
 * @param {number} params.pageNo 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.tableName 表名搜索（可选）
 */
export const getDataSourceWithTables = async (id, params = {}) => {
  try {
    const response = await get(`/datasource/${id}/tables`, params)
    return response
  } catch (error) {
    console.error('获取数据源详情失败:', error)
    throw error
  }
};

/**
 * 获取字段详情
 * @param {Object} params 查询参数
 * @param {string} params.dataBaseId 数据库ID
 * @param {string} params.tableName 表名
 * @param {string} params.sheetId 表格ID（Excel文件）
 */
export const getFieldDetail = async (params) => {
  try {
    const response = await get('/datasource/field/detail', params)
    return response
  } catch (error) {
    console.error('获取字段详情失败:', error)
    throw error
  }
};

/**
 * 获取数据预览
 * @param {Object} params 查询参数
 * @param {string} params.dataBaseId 数据库ID
 * @param {string} params.tableName 表名
 * @param {string} params.sheetId 表格ID（Excel文件）
 * @param {number} params.pageNo 页码
 * @param {number} params.pageSize 每页大小
 */
export const getDataDetail = async (params) => {
  try {
    const response = await get('/datasource/data/preview', params)
    return response
  } catch (error) {
    console.error('获取数据预览失败:', error)
    throw error
  }
};

/**
 * 更新数据源
 * @param {Object} data 数据源信息
 * @param {string} data.id 数据源ID
 * @param {string} data.name 数据源名称
 */
export const updateDataSource = async (data) => {
  try {
    const response = await put(`/datasource/${data.id}`, data)
    return response
  } catch (error) {
    console.error('更新数据源失败:', error)
    throw error
  }
};

/**
 * 删除数据源
 * @param {string} id 数据源ID
 */
export const deleteDataSource = async (id) => {
  try {
    const response = await del(`/datasource/${id}`)
    return response
  } catch (error) {
    console.error('删除数据源失败:', error)
    throw error
  }
};

/**
 * 获取数据库配置
 * @param {string} typeId 数据库类型ID
 */
export const getDatabaseConfig = async (typeId) => {
  try {
    const response = await get(`/datasource/database/config/${typeId}`)
    return response
  } catch (error) {
    console.error('获取数据库配置失败:', error)
    throw error
  }
};
